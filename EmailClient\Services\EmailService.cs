using EmailClient.Data;
using EmailClient.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmailClient.Services;

public class EmailService : IEmailService
{
    private readonly EmailDbContext _context;
    private readonly IImapService _imapService;
    private readonly ILogger<EmailService> _logger;

    public EmailService(EmailDbContext context, IImapService imapService, ILogger<EmailService> logger)
    {
        _context = context;
        _imapService = imapService;
        _logger = logger;
    }

    public async Task<IEnumerable<EmailMessage>> GetUnifiedInboxAsync()
    {
        return await _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .Where(m => !m.IsDeleted && m.Folder!.Type == FolderType.Inbox)
            .OrderByDescending(m => m.DateReceived)
            .Take(100)
            .ToListAsync();
    }

    public async Task<IEnumerable<EmailMessage>> GetMessagesForAccountAsync(int accountId)
    {
        return await _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .Where(m => m.AccountId == accountId && !m.IsDeleted)
            .OrderByDescending(m => m.DateReceived)
            .ToListAsync();
    }

    public async Task<IEnumerable<EmailMessage>> GetMessagesForFolderAsync(int folderId)
    {
        return await _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .Where(m => m.FolderId == folderId && !m.IsDeleted)
            .OrderByDescending(m => m.DateReceived)
            .ToListAsync();
    }

    public async Task<EmailMessage?> GetMessageAsync(int messageId)
    {
        return await _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .Include(m => m.Attachments)
            .FirstOrDefaultAsync(m => m.Id == messageId);
    }

    public async Task SyncAccountAsync(int accountId)
    {
        var account = await _context.Accounts.FindAsync(accountId);
        if (account == null || !account.IsEnabled)
        {
            _logger.LogWarning("Account {AccountId} not found or disabled", accountId);
            return;
        }

        try
        {
            _logger.LogInformation("Starting sync for account {Email}", account.EmailAddress);

            // Sync folders first
            var folders = await _imapService.GetFoldersAsync(account);
            await SyncFoldersAsync(account, folders);

            // Sync messages for each folder
            var dbFolders = await _context.Folders
                .Where(f => f.AccountId == accountId)
                .ToListAsync();

            foreach (var folder in dbFolders)
            {
                await SyncFolderMessagesAsync(account, folder);
            }

            account.LastSyncAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Completed sync for account {Email}", account.EmailAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync account {Email}", account.EmailAddress);
        }
    }

    private async Task SyncFoldersAsync(EmailAccount account, IEnumerable<EmailFolder> folders)
    {
        var existingFolders = await _context.Folders
            .Where(f => f.AccountId == account.Id)
            .ToListAsync();

        foreach (var folder in folders)
        {
            var existing = existingFolders.FirstOrDefault(f => f.FullName == folder.FullName);
            if (existing == null)
            {
                _context.Folders.Add(folder);
            }
            else
            {
                existing.Name = folder.Name;
                existing.Type = folder.Type;
                existing.UnreadCount = folder.UnreadCount;
                existing.TotalCount = folder.TotalCount;
                existing.LastSyncAt = folder.LastSyncAt;
            }
        }

        await _context.SaveChangesAsync();
    }

    private async Task SyncFolderMessagesAsync(EmailAccount account, EmailFolder folder)
    {
        try
        {
            var messages = await _imapService.GetMessagesAsync(account, folder, 50);
            
            foreach (var message in messages)
            {
                var existing = await _context.Messages
                    .FirstOrDefaultAsync(m => m.AccountId == account.Id && m.MessageId == message.MessageId);

                if (existing == null)
                {
                    _context.Messages.Add(message);
                }
                else
                {
                    // Update existing message if needed
                    existing.IsRead = message.IsRead;
                    existing.IsFlagged = message.IsFlagged;
                }
            }

            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync messages for folder {FolderName}", folder.Name);
        }
    }

    public async Task SyncAllAccountsAsync()
    {
        var accounts = await _context.Accounts
            .Where(a => a.IsEnabled)
            .ToListAsync();

        var tasks = accounts.Select(account => SyncAccountAsync(account.Id));
        await Task.WhenAll(tasks);
    }

    public async Task<IEnumerable<EmailMessage>> SearchAsync(string query)
    {
        return await _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .Where(m => !m.IsDeleted && 
                       (m.Subject.Contains(query) || 
                        m.FromAddress.Contains(query) || 
                        m.FromName.Contains(query) ||
                        m.TextBody.Contains(query)))
            .OrderByDescending(m => m.DateReceived)
            .Take(100)
            .ToListAsync();
    }

    public async Task MarkAsReadAsync(int messageId)
    {
        var message = await GetMessageAsync(messageId);
        if (message != null)
        {
            message.IsRead = true;
            await _context.SaveChangesAsync();

            // Also mark as read on the server
            try
            {
                await _imapService.MarkAsReadAsync(message.Account, message.Folder!, message.MessageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to mark message as read on server");
            }
        }
    }

    public async Task MarkAsUnreadAsync(int messageId)
    {
        var message = await GetMessageAsync(messageId);
        if (message != null)
        {
            message.IsRead = false;
            await _context.SaveChangesAsync();

            // Also mark as unread on the server
            try
            {
                await _imapService.MarkAsUnreadAsync(message.Account, message.Folder!, message.MessageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to mark message as unread on server");
            }
        }
    }

    public async Task DeleteMessageAsync(int messageId)
    {
        var message = await GetMessageAsync(messageId);
        if (message != null)
        {
            message.IsDeleted = true;
            await _context.SaveChangesAsync();

            // Also delete on the server
            try
            {
                await _imapService.DeleteMessageAsync(message.Account, message.Folder!, message.MessageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete message on server");
            }
        }
    }

    public async Task MoveMessageAsync(int messageId, int targetFolderId)
    {
        var message = await GetMessageAsync(messageId);
        var targetFolder = await _context.Folders.FindAsync(targetFolderId);
        
        if (message != null && targetFolder != null)
        {
            var oldFolderId = message.FolderId;
            message.FolderId = targetFolderId;
            await _context.SaveChangesAsync();

            // Also move on the server
            try
            {
                var sourceFolder = await _context.Folders.FindAsync(oldFolderId);
                if (sourceFolder != null)
                {
                    await _imapService.MoveMessageAsync(message.Account, sourceFolder, targetFolder, message.MessageId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to move message on server");
            }
        }
    }
}
