using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using EmailClient.Data;
using EmailClient.Services;
using EmailClient.ViewModels;
using EmailClient.Views;
using Serilog;
using System.Windows;

namespace EmailClient;

public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.File("logs/emailclient-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        // Build the host
        _host = Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // Database
                services.AddDbContext<EmailDbContext>(options =>
                    options.UseSqlite("Data Source=emailclient.db"));

                // Services
                services.AddSingleton<IImapService, ImapService>();
                services.AddSingleton<IEmailService, EmailService>();
                services.AddSingleton<IAccountService, AccountService>();
                services.AddSingleton<ISettingsService, SettingsService>();
                services.AddSingleton<ISyncService, SyncService>();
                services.AddSingleton<IMultiAccountService, MultiAccountService>();

                // ViewModels
                services.AddTransient<MainWindowViewModel>();
                services.AddTransient<AccountSetupViewModel>();
                services.AddTransient<AccountDashboardViewModel>();
                services.AddTransient<SettingsViewModel>();
                services.AddTransient<EmailListViewModel>();
                services.AddTransient<EmailDetailViewModel>();

                // Views
                services.AddTransient<MainWindow>();
            })
            .Build();

        // Ensure database is created
        using (var scope = _host.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<EmailDbContext>();
            context.Database.EnsureCreated();
        }

        // Start the sync service
        var syncService = _host.Services.GetRequiredService<ISyncService>();
        _ = syncService.StartAsync();

        // Start the main window
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        // Stop the sync service
        if (_host != null)
        {
            var syncService = _host.Services.GetService<ISyncService>();
            if (syncService != null)
            {
                await syncService.StopAsync();
            }
        }

        _host?.Dispose();
        Log.CloseAndFlush();
        base.OnExit(e);
    }
}
