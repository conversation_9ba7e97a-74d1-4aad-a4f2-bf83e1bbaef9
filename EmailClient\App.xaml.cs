using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using EmailClient.Data;
using EmailClient.Services;
using EmailClient.ViewModels;
using EmailClient.Views;
using Serilog;
using System.Windows;

namespace EmailClient;

public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.File("logs/emailclient-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        // Build the host
        _host = Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // Database
                services.AddDbContext<EmailDbContext>(options =>
                    options.UseSqlite("Data Source=emailclient.db"));

                // Services
                services.AddSingleton<IImapService, ImapService>();
                services.AddSingleton<IEmailService, EmailService>();
                services.AddSingleton<IAccountService, AccountService>();
                services.AddSingleton<ISettingsService, SettingsService>();

                // ViewModels
                services.AddTransient<MainWindowViewModel>();
                services.AddTransient<AccountSetupViewModel>();
                services.AddTransient<EmailListViewModel>();
                services.AddTransient<EmailDetailViewModel>();

                // Views
                services.AddTransient<MainWindow>();
            })
            .Build();

        // Ensure database is created
        using (var scope = _host.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<EmailDbContext>();
            context.Database.EnsureCreated();
        }

        // Start the main window
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        Log.CloseAndFlush();
        base.OnExit(e);
    }
}
