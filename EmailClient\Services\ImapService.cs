using EmailClient.Models;
using MailKit.Net.Imap;
using MailKit.Security;
using MailKit.Search;
using MailKit;
using MimeKit;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace EmailClient.Services;

public class ImapService : IImapService
{
    private readonly ILogger<ImapService> _logger;

    public ImapService(ILogger<ImapService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> TestConnectionAsync(EmailAccount account)
    {
        try
        {
            using var client = await ConnectAsync(account);
            return client.IsConnected && client.IsAuthenticated;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test connection for account {Email}", account.EmailAddress);
            return false;
        }
    }

    public async Task<ImapClient> ConnectAsync(EmailAccount account)
    {
        var client = new ImapClient();
        
        try
        {
            await client.ConnectAsync(account.ImapServer, account.ImapPort, account.UseSsl);
            await client.AuthenticateAsync(account.Username, account.Password);
            return client;
        }
        catch
        {
            client.Dispose();
            throw;
        }
    }

    public async Task<IEnumerable<EmailFolder>> GetFoldersAsync(EmailAccount account)
    {
        using var client = await ConnectAsync(account);
        var folders = new List<EmailFolder>();

        var imapFolders = await client.GetFoldersAsync(client.PersonalNamespaces[0]);
        
        foreach (var folder in imapFolders)
        {
            var emailFolder = new EmailFolder
            {
                AccountId = account.Id,
                Name = folder.Name,
                FullName = folder.FullName,
                Type = DetermineFolderType(folder.Name, folder.FullName),
                LastSyncAt = DateTime.UtcNow
            };

            // Get folder counts
            try
            {
                await folder.OpenAsync(FolderAccess.ReadOnly);
                emailFolder.TotalCount = folder.Count;
                emailFolder.UnreadCount = folder.Unread;
                await folder.CloseAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get counts for folder {FolderName}", folder.FullName);
            }

            folders.Add(emailFolder);
        }

        return folders;
    }

    public async Task<IEnumerable<EmailMessage>> GetMessagesAsync(EmailAccount account, EmailFolder folder, int limit = 50)
    {
        using var client = await ConnectAsync(account);
        var imapFolder = client.GetFolder(folder.FullName);
        await imapFolder.OpenAsync(FolderAccess.ReadOnly);

        var messages = new List<EmailMessage>();
        var count = Math.Min(limit, imapFolder.Count);
        
        if (count == 0) return messages;

        var startIndex = Math.Max(0, imapFolder.Count - count);
        var uids = await imapFolder.SearchAsync(SearchQuery.All);
        
        for (int i = startIndex; i < imapFolder.Count && messages.Count < limit; i++)
        {
            try
            {
                var message = await imapFolder.GetMessageAsync(i);
                var emailMessage = ConvertToEmailMessage(message, account.Id, folder.Id, uids[i].ToString());
                messages.Add(emailMessage);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch message at index {Index}", i);
            }
        }

        await imapFolder.CloseAsync();
        return messages.OrderByDescending(m => m.DateReceived);
    }

    private EmailMessage ConvertToEmailMessage(MimeMessage mimeMessage, int accountId, int? folderId, string uid)
    {
        var emailMessage = new EmailMessage
        {
            MessageId = uid,
            AccountId = accountId,
            FolderId = folderId,
            Subject = mimeMessage.Subject ?? string.Empty,
            FromAddress = mimeMessage.From.Mailboxes.FirstOrDefault()?.Address ?? string.Empty,
            FromName = mimeMessage.From.Mailboxes.FirstOrDefault()?.Name ?? string.Empty,
            ToAddresses = JsonSerializer.Serialize(mimeMessage.To.Mailboxes.Select(m => new { Name = m.Name, Address = m.Address })),
            CcAddresses = JsonSerializer.Serialize(mimeMessage.Cc.Mailboxes.Select(m => new { Name = m.Name, Address = m.Address })),
            BccAddresses = JsonSerializer.Serialize(mimeMessage.Bcc.Mailboxes.Select(m => new { Name = m.Name, Address = m.Address })),
            DateSent = mimeMessage.Date.DateTime,
            DateReceived = DateTime.UtcNow,
            TextBody = mimeMessage.TextBody ?? string.Empty,
            HtmlBody = mimeMessage.HtmlBody ?? string.Empty,
            HasAttachments = mimeMessage.Attachments.Any()
        };

        return emailMessage;
    }

    private FolderType DetermineFolderType(string name, string fullName)
    {
        var lowerName = name.ToLowerInvariant();
        var lowerFullName = fullName.ToLowerInvariant();

        if (lowerName.Contains("inbox") || lowerFullName.Contains("inbox"))
            return FolderType.Inbox;
        if (lowerName.Contains("sent") || lowerFullName.Contains("sent"))
            return FolderType.Sent;
        if (lowerName.Contains("draft") || lowerFullName.Contains("draft"))
            return FolderType.Drafts;
        if (lowerName.Contains("trash") || lowerName.Contains("deleted") || lowerFullName.Contains("trash"))
            return FolderType.Trash;
        if (lowerName.Contains("spam") || lowerName.Contains("junk") || lowerFullName.Contains("spam"))
            return FolderType.Spam;
        if (lowerName.Contains("archive") || lowerFullName.Contains("archive"))
            return FolderType.Archive;

        return FolderType.Custom;
    }

    public async Task<EmailMessage> GetMessageDetailsAsync(EmailAccount account, EmailFolder folder, string messageId)
    {
        using var client = await ConnectAsync(account);
        var imapFolder = client.GetFolder(folder.FullName);
        await imapFolder.OpenAsync(FolderAccess.ReadOnly);

        var uid = new UniqueId(uint.Parse(messageId));
        var message = await imapFolder.GetMessageAsync(uid);
        var emailMessage = ConvertToEmailMessage(message, account.Id, folder.Id, messageId);

        await imapFolder.CloseAsync();
        return emailMessage;
    }

    public async Task MarkAsReadAsync(EmailAccount account, EmailFolder folder, string messageId)
    {
        using var client = await ConnectAsync(account);
        var imapFolder = client.GetFolder(folder.FullName);
        await imapFolder.OpenAsync(FolderAccess.ReadWrite);

        var uid = new UniqueId(uint.Parse(messageId));
        await imapFolder.AddFlagsAsync(uid, MessageFlags.Seen, true);

        await imapFolder.CloseAsync();
    }

    public async Task MarkAsUnreadAsync(EmailAccount account, EmailFolder folder, string messageId)
    {
        using var client = await ConnectAsync(account);
        var imapFolder = client.GetFolder(folder.FullName);
        await imapFolder.OpenAsync(FolderAccess.ReadWrite);

        var uid = new UniqueId(uint.Parse(messageId));
        await imapFolder.RemoveFlagsAsync(uid, MessageFlags.Seen, true);

        await imapFolder.CloseAsync();
    }

    public async Task DeleteMessageAsync(EmailAccount account, EmailFolder folder, string messageId)
    {
        using var client = await ConnectAsync(account);
        var imapFolder = client.GetFolder(folder.FullName);
        await imapFolder.OpenAsync(FolderAccess.ReadWrite);

        var uid = new UniqueId(uint.Parse(messageId));
        await imapFolder.AddFlagsAsync(uid, MessageFlags.Deleted, true);
        await imapFolder.ExpungeAsync();

        await imapFolder.CloseAsync();
    }

    public async Task MoveMessageAsync(EmailAccount account, EmailFolder sourceFolder, EmailFolder targetFolder, string messageId)
    {
        using var client = await ConnectAsync(account);
        var sourceImapFolder = client.GetFolder(sourceFolder.FullName);
        var targetImapFolder = client.GetFolder(targetFolder.FullName);
        
        await sourceImapFolder.OpenAsync(FolderAccess.ReadWrite);

        var uid = new UniqueId(uint.Parse(messageId));
        await sourceImapFolder.MoveToAsync(uid, targetImapFolder);

        await sourceImapFolder.CloseAsync();
    }

    public async Task<IEnumerable<EmailMessage>> SearchMessagesAsync(EmailAccount account, EmailFolder folder, string searchQuery)
    {
        using var client = await ConnectAsync(account);
        var imapFolder = client.GetFolder(folder.FullName);
        await imapFolder.OpenAsync(FolderAccess.ReadOnly);

        var query = SearchQuery.SubjectContains(searchQuery)
            .Or(SearchQuery.BodyContains(searchQuery))
            .Or(SearchQuery.FromContains(searchQuery));

        var uids = await imapFolder.SearchAsync(query);
        var messages = new List<EmailMessage>();

        foreach (var uid in uids.Take(100)) // Limit search results
        {
            try
            {
                var message = await imapFolder.GetMessageAsync(uid);
                var emailMessage = ConvertToEmailMessage(message, account.Id, folder.Id, uid.ToString());
                messages.Add(emailMessage);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch search result message {Uid}", uid);
            }
        }

        await imapFolder.CloseAsync();
        return messages.OrderByDescending(m => m.DateReceived);
    }
}
