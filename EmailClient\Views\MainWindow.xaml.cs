using EmailClient.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace EmailClient.Views;

public partial class MainWindow : Window
{
    private MainWindowViewModel? _viewModel;

    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
        _viewModel = viewModel;

        // Subscribe to property changes to update email body
        _viewModel.PropertyChanged += ViewModel_PropertyChanged;
    }

    private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(MainWindowViewModel.SelectedMessage))
        {
            UpdateEmailBody();
        }
    }

    private void UpdateEmailBody()
    {
        if (_viewModel?.SelectedMessage == null)
        {
            EmailBodyBrowser.NavigateToString("<html><body></body></html>");
            return;
        }

        var message = _viewModel.SelectedMessage;
        var htmlContent = !string.IsNullOrEmpty(message.HtmlBody)
            ? message.HtmlBody
            : $"<html><body><pre>{System.Web.HttpUtility.HtmlEncode(message.TextBody)}</pre></body></html>";

        EmailBodyBrowser.NavigateToString(htmlContent);
    }

    private void FolderTree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (DataContext is MainWindowViewModel viewModel && e.NewValue != null)
        {
            viewModel.SelectedFolder = e.NewValue;
        }
    }

    private void SearchBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is MainWindowViewModel viewModel)
        {
            viewModel.SearchCommand.Execute(null);
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        if (_viewModel != null)
        {
            _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
        }
        base.OnClosed(e);
    }
}
