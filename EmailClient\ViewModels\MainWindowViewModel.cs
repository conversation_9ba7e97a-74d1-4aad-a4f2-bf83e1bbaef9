using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;

namespace EmailClient.ViewModels;

public partial class MainWindowViewModel : ViewModelBase
{
    private readonly IEmailService _emailService;
    private readonly IAccountService _accountService;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private ObservableCollection<FolderTreeItemViewModel> _folderTree = new();

    [ObservableProperty]
    private ObservableCollection<EmailMessageViewModel> _emailMessages = new();

    [ObservableProperty]
    private EmailMessageViewModel? _selectedMessage;

    [ObservableProperty]
    private object? _selectedFolder;

    [ObservableProperty]
    private string _searchQuery = string.Empty;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private int _totalEmailCount;

    public MainWindowViewModel(IEmailService emailService, IAccountService accountService, ILogger<MainWindowViewModel> logger)
    {
        _emailService = emailService;
        _accountService = accountService;
        _logger = logger;

        _ = InitializeAsync();
    }

    private async Task InitializeAsync()
    {
        try
        {
            StatusMessage = "Loading...";
            await LoadFolderTreeAsync();
            await LoadUnifiedInboxAsync();
            StatusMessage = "Ready";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize main window");
            StatusMessage = "Error loading data";
        }
    }

    private async Task LoadFolderTreeAsync()
    {
        var accounts = await _accountService.GetAllAccountsAsync();
        FolderTree.Clear();

        // Add unified inbox
        var unifiedInbox = new FolderTreeItemViewModel
        {
            Name = "Unified Inbox",
            FullName = "unified",
            Type = FolderType.Inbox,
            IsAccount = false
        };
        FolderTree.Add(unifiedInbox);

        // Add accounts and their folders
        foreach (var account in accounts)
        {
            var accountNode = FolderTreeItemViewModel.FromAccount(account);
            var folders = await _accountService.GetAccountFoldersAsync(account.Id);

            foreach (var folder in folders)
            {
                var folderNode = FolderTreeItemViewModel.FromFolder(folder);
                accountNode.Children.Add(folderNode);
            }

            FolderTree.Add(accountNode);
        }
    }

    private async Task LoadUnifiedInboxAsync()
    {
        try
        {
            var messages = await _emailService.GetUnifiedInboxAsync();
            EmailMessages.Clear();
            
            foreach (var message in messages)
            {
                EmailMessages.Add(new EmailMessageViewModel(message));
            }

            TotalEmailCount = EmailMessages.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load unified inbox");
        }
    }

    partial void OnSelectedFolderChanged(object? value)
    {
        _ = LoadMessagesForSelectedFolderAsync();
    }

    private async Task LoadMessagesForSelectedFolderAsync()
    {
        if (SelectedFolder is not FolderTreeItemViewModel folder)
            return;

        try
        {
            StatusMessage = "Loading messages...";
            EmailMessages.Clear();

            IEnumerable<EmailMessage> messages;

            if (folder.FullName == "unified")
            {
                messages = await _emailService.GetUnifiedInboxAsync();
            }
            else if (folder.IsAccount && folder.AccountId.HasValue)
            {
                messages = await _emailService.GetMessagesForAccountAsync(folder.AccountId.Value);
            }
            else if (folder.FolderId.HasValue)
            {
                messages = await _emailService.GetMessagesForFolderAsync(folder.FolderId.Value);
            }
            else
            {
                return;
            }

            foreach (var message in messages)
            {
                EmailMessages.Add(new EmailMessageViewModel(message));
            }

            TotalEmailCount = EmailMessages.Count;
            StatusMessage = "Ready";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load messages for folder {FolderName}", folder.Name);
            StatusMessage = "Error loading messages";
        }
    }

    [RelayCommand]
    private async Task AddAccount()
    {
        // TODO: Open account setup dialog
        StatusMessage = "Add account functionality coming soon...";
    }

    [RelayCommand]
    private async Task SyncAll()
    {
        try
        {
            StatusMessage = "Syncing all accounts...";
            await _emailService.SyncAllAccountsAsync();
            await LoadFolderTreeAsync();
            await LoadMessagesForSelectedFolderAsync();
            StatusMessage = "Sync completed";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync all accounts");
            StatusMessage = "Sync failed";
        }
    }

    [RelayCommand]
    private async Task Refresh()
    {
        await LoadMessagesForSelectedFolderAsync();
    }

    [RelayCommand]
    private async Task Search()
    {
        if (string.IsNullOrWhiteSpace(SearchQuery))
        {
            await LoadMessagesForSelectedFolderAsync();
            return;
        }

        try
        {
            StatusMessage = "Searching...";
            var results = await _emailService.SearchAsync(SearchQuery);
            EmailMessages.Clear();

            foreach (var message in results)
            {
                EmailMessages.Add(new EmailMessageViewModel(message));
            }

            TotalEmailCount = EmailMessages.Count;
            StatusMessage = $"Found {EmailMessages.Count} results";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Search failed for query: {Query}", SearchQuery);
            StatusMessage = "Search failed";
        }
    }

    [RelayCommand]
    private void Settings()
    {
        StatusMessage = "Settings functionality coming soon...";
    }

    [RelayCommand]
    private void Exit()
    {
        Application.Current.Shutdown();
    }

    [RelayCommand]
    private void About()
    {
        MessageBox.Show("Email Client v1.0\nA unified IMAP email client for Windows", "About", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    public string MarkReadButtonText => SelectedMessage?.IsRead == true ? "Mark Unread" : "Mark Read";

    [RelayCommand]
    private async Task MarkRead()
    {
        if (SelectedMessage == null) return;

        try
        {
            if (SelectedMessage.IsRead)
            {
                await _emailService.MarkAsUnreadAsync(SelectedMessage.Id);
            }
            else
            {
                await _emailService.MarkAsReadAsync(SelectedMessage.Id);
            }

            // Refresh the message list
            await LoadMessagesForSelectedFolderAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to toggle read status");
            StatusMessage = "Failed to update message";
        }
    }

    [RelayCommand]
    private async Task Delete()
    {
        if (SelectedMessage == null) return;

        var result = MessageBox.Show("Are you sure you want to delete this message?", "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result != MessageBoxResult.Yes) return;

        try
        {
            await _emailService.DeleteMessageAsync(SelectedMessage.Id);
            await LoadMessagesForSelectedFolderAsync();
            StatusMessage = "Message deleted";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete message");
            StatusMessage = "Failed to delete message";
        }
    }

    [RelayCommand]
    private void Reply()
    {
        StatusMessage = "Reply functionality coming soon...";
    }

    [RelayCommand]
    private void Forward()
    {
        StatusMessage = "Forward functionality coming soon...";
    }
}
