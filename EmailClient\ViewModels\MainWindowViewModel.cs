using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using EmailClient.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;

namespace EmailClient.ViewModels;

public partial class MainWindowViewModel : ViewModelBase
{
    private readonly IEmailService _emailService;
    private readonly IAccountService _accountService;
    private readonly IMultiAccountService _multiAccountService;
    private readonly ILogger<MainWindowViewModel> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ISyncService _syncService;

    [ObservableProperty]
    private ObservableCollection<FolderTreeItemViewModel> _folderTree = new();

    [ObservableProperty]
    private ObservableCollection<EmailMessageViewModel> _emailMessages = new();

    [ObservableProperty]
    private EmailMessageViewModel? _selectedMessage;

    [ObservableProperty]
    private object? _selectedFolder;

    [ObservableProperty]
    private string _searchQuery = string.Empty;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private int _totalEmailCount;

    public MainWindowViewModel(IEmailService emailService, IAccountService accountService, IMultiAccountService multiAccountService, ILogger<MainWindowViewModel> logger, IServiceProvider serviceProvider, ISyncService syncService)
    {
        _emailService = emailService;
        _accountService = accountService;
        _multiAccountService = multiAccountService;
        _logger = logger;
        _serviceProvider = serviceProvider;
        _syncService = syncService;

        // Subscribe to sync events
        _syncService.SyncCompleted += OnSyncCompleted;

        _ = InitializeAsync();
    }

    private void OnSyncCompleted(object? sender, SyncCompletedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            if (e.Success)
            {
                StatusMessage = $"Sync completed - {e.AccountsSynced} accounts synced";
                _ = LoadMessagesForSelectedFolderAsync(); // Refresh current view
            }
            else
            {
                StatusMessage = $"Sync failed: {e.ErrorMessage}";
            }
        });
    }

    private async Task InitializeAsync()
    {
        try
        {
            StatusMessage = "Loading...";
            await LoadFolderTreeAsync();
            await LoadUnifiedInboxAsync();
            StatusMessage = "Ready";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize main window");
            StatusMessage = "Error loading data";
        }
    }

    private async Task LoadFolderTreeAsync()
    {
        var accounts = await _accountService.GetAllAccountsAsync();
        FolderTree.Clear();

        // Add unified inbox
        var unifiedInbox = new FolderTreeItemViewModel
        {
            Name = "Unified Inbox",
            FullName = "unified",
            Type = FolderType.Inbox,
            IsAccount = false
        };
        FolderTree.Add(unifiedInbox);

        // Add accounts and their folders
        foreach (var account in accounts)
        {
            var accountNode = FolderTreeItemViewModel.FromAccount(account);
            var folders = await _accountService.GetAccountFoldersAsync(account.Id);

            foreach (var folder in folders)
            {
                var folderNode = FolderTreeItemViewModel.FromFolder(folder);
                accountNode.Children.Add(folderNode);
            }

            FolderTree.Add(accountNode);
        }
    }

    private async Task LoadUnifiedInboxAsync()
    {
        try
        {
            var result = await _multiAccountService.GetUnifiedInboxAsync(50, 1);
            EmailMessages.Clear();

            foreach (var message in result.Messages)
            {
                EmailMessages.Add(new EmailMessageViewModel(message));
            }

            TotalEmailCount = result.TotalCount;

            // Update status with account breakdown
            if (result.MessageCountByAccount.Any())
            {
                var accountBreakdown = string.Join(", ",
                    result.MessageCountByAccount.Select(kvp => $"Account {kvp.Key}: {kvp.Value}"));
                StatusMessage = $"Unified Inbox loaded - {result.TotalCount} total ({result.UnreadCount} unread)";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load unified inbox");
            StatusMessage = "Failed to load unified inbox";
        }
    }

    partial void OnSelectedFolderChanged(object? value)
    {
        _ = LoadMessagesForSelectedFolderAsync();
    }

    partial void OnSelectedMessageChanged(EmailMessageViewModel? value)
    {
        OnPropertyChanged(nameof(MarkReadButtonText));
        _ = LoadMessageBodyAsync();
    }

    private async Task LoadMessageBodyAsync()
    {
        if (SelectedMessage == null) return;

        try
        {
            // Mark as read when viewing
            if (!SelectedMessage.IsRead)
            {
                await _emailService.MarkAsReadAsync(SelectedMessage.Id);
                SelectedMessage.GetModel().IsRead = true;
                OnPropertyChanged(nameof(MarkReadButtonText));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark message as read");
        }
    }

    private async Task LoadMessagesForSelectedFolderAsync()
    {
        if (SelectedFolder is not FolderTreeItemViewModel folder)
            return;

        try
        {
            StatusMessage = "Loading messages...";
            EmailMessages.Clear();

            IEnumerable<EmailMessage> messages;

            if (folder.FullName == "unified")
            {
                messages = await _emailService.GetUnifiedInboxAsync();
            }
            else if (folder.IsAccount && folder.AccountId.HasValue)
            {
                messages = await _emailService.GetMessagesForAccountAsync(folder.AccountId.Value);
            }
            else if (folder.FolderId.HasValue)
            {
                messages = await _emailService.GetMessagesForFolderAsync(folder.FolderId.Value);
            }
            else
            {
                return;
            }

            foreach (var message in messages)
            {
                EmailMessages.Add(new EmailMessageViewModel(message));
            }

            TotalEmailCount = EmailMessages.Count;
            StatusMessage = "Ready";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load messages for folder {FolderName}", folder.Name);
            StatusMessage = "Error loading messages";
        }
    }

    [RelayCommand]
    private async Task AddAccount()
    {
        try
        {
            var accountSetupViewModel = _serviceProvider.GetRequiredService<AccountSetupViewModel>();
            var accountSetupWindow = new AccountSetupWindow(accountSetupViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            accountSetupViewModel.SetWindow(accountSetupWindow);
            accountSetupWindow.ShowDialog();

            if (accountSetupViewModel.DialogResult)
            {
                StatusMessage = "Account added successfully";
                await LoadFolderTreeAsync();
                await LoadUnifiedInboxAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open account setup dialog");
            StatusMessage = "Failed to open account setup";
        }
    }

    [RelayCommand]
    private async Task SyncAll()
    {
        try
        {
            StatusMessage = "Syncing all accounts...";
            await _syncService.SyncNowAsync();
            await LoadFolderTreeAsync();
            // Note: LoadMessagesForSelectedFolderAsync will be called by OnSyncCompleted
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync all accounts");
            StatusMessage = "Sync failed";
        }
    }

    [RelayCommand]
    private async Task Refresh()
    {
        await LoadMessagesForSelectedFolderAsync();
    }

    [RelayCommand]
    private async Task Search()
    {
        if (string.IsNullOrWhiteSpace(SearchQuery))
        {
            await LoadMessagesForSelectedFolderAsync();
            return;
        }

        try
        {
            StatusMessage = "Searching across all accounts...";
            var searchResult = await _multiAccountService.SearchAcrossAccountsAsync(SearchQuery, 100);
            EmailMessages.Clear();

            foreach (var message in searchResult.Messages)
            {
                EmailMessages.Add(new EmailMessageViewModel(message));
            }

            TotalEmailCount = searchResult.TotalResults;

            // Show search results breakdown by account
            var accountBreakdown = string.Join(", ",
                searchResult.ResultsByAccount.Select(kvp => $"Account {kvp.Key}: {kvp.Value}"));
            StatusMessage = $"Found {searchResult.TotalResults} results in {searchResult.SearchDuration.TotalMilliseconds:F0}ms ({accountBreakdown})";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Search failed for query: {Query}", SearchQuery);
            StatusMessage = "Search failed";
        }
    }

    [RelayCommand]
    private void ShowDashboard()
    {
        try
        {
            var dashboardViewModel = _serviceProvider.GetRequiredService<AccountDashboardViewModel>();
            var dashboardWindow = new Views.AccountDashboardWindow(dashboardViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            dashboardWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open account dashboard");
            StatusMessage = "Failed to open account dashboard";
        }
    }

    [RelayCommand]
    private void Settings()
    {
        try
        {
            var settingsViewModel = _serviceProvider.GetRequiredService<SettingsViewModel>();
            var settingsWindow = new Views.SettingsWindow(settingsViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            settingsViewModel.SetWindow(settingsWindow);
            settingsWindow.ShowDialog();

            if (settingsViewModel.DialogResult)
            {
                StatusMessage = "Settings saved successfully";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open settings dialog");
            StatusMessage = "Failed to open settings";
        }
    }

    [RelayCommand]
    private void Exit()
    {
        Application.Current.Shutdown();
    }

    [RelayCommand]
    private void About()
    {
        MessageBox.Show("Email Client v1.0\nA unified IMAP email client for Windows", "About", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    public string MarkReadButtonText => SelectedMessage?.IsRead == true ? "Mark Unread" : "Mark Read";

    [RelayCommand]
    private async Task MarkRead()
    {
        if (SelectedMessage == null) return;

        try
        {
            if (SelectedMessage.IsRead)
            {
                await _emailService.MarkAsUnreadAsync(SelectedMessage.Id);
            }
            else
            {
                await _emailService.MarkAsReadAsync(SelectedMessage.Id);
            }

            // Refresh the message list
            await LoadMessagesForSelectedFolderAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to toggle read status");
            StatusMessage = "Failed to update message";
        }
    }

    [RelayCommand]
    private async Task Delete()
    {
        if (SelectedMessage == null) return;

        var result = MessageBox.Show("Are you sure you want to delete this message?", "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result != MessageBoxResult.Yes) return;

        try
        {
            await _emailService.DeleteMessageAsync(SelectedMessage.Id);
            await LoadMessagesForSelectedFolderAsync();
            StatusMessage = "Message deleted";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete message");
            StatusMessage = "Failed to delete message";
        }
    }

    [RelayCommand]
    private void Reply()
    {
        StatusMessage = "Reply functionality coming soon...";
    }

    [RelayCommand]
    private void Forward()
    {
        StatusMessage = "Forward functionality coming soon...";
    }
}
